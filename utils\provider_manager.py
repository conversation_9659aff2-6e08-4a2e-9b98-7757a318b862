"""
Provider Manager for BlendPro v2.1.0
Manages AI provider registration, validation, and operations
"""

import json
import os
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from ..config.providers import AIProvider, ProviderType, get_default_providers, validate_provider_config
from ..config.agent_configs import AgentConfiguration, AgentType, get_default_agent_configurations
from ..utils.logger import get_logger

class ProviderManager:
    """Manages AI providers and their configurations"""
    
    def __init__(self):
        self.logger = get_logger("BlendPro.ProviderManager")
        self._providers: Dict[str, AIProvider] = {}
        self._agent_configs: Dict[AgentType, AgentConfiguration] = {}
        self._config_file = self._get_config_file_path()
        
        # Load configurations
        self._load_configurations()
    
    def _get_config_file_path(self) -> str:
        """Get path to provider configuration file"""
        # Store in Blender's user config directory
        import bpy
        config_dir = bpy.utils.user_resource('CONFIG', path="blendpro")
        if not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)
        return os.path.join(config_dir, "provider_config.json")
    
    def _load_configurations(self):
        """Load provider and agent configurations from file with robust error handling"""
        try:
            if os.path.exists(self._config_file):
                with open(self._config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Validate that data is a dictionary
                if not isinstance(data, dict):
                    self.logger.warning(f"Configuration file contains invalid data type. Expected dict, got {type(data)}. Using defaults.")
                    self._load_default_configurations()
                    return

                # Load providers
                providers_data = data.get("providers", {})
                if isinstance(providers_data, dict):
                    for name, provider_data in providers_data.items():
                        try:
                            self._providers[name] = AIProvider.from_dict(provider_data)
                        except Exception as e:
                            self.logger.error(f"Failed to load provider {name}: {e}")
                else:
                    self.logger.warning("Invalid providers data format in config file")

                # Load agent configurations
                agents_data = data.get("agent_configurations", {})
                if isinstance(agents_data, dict):
                    for agent_type_str, config_data in agents_data.items():
                        try:
                            agent_type = AgentType(agent_type_str)
                            self._agent_configs[agent_type] = AgentConfiguration.from_dict(config_data)
                        except Exception as e:
                            self.logger.error(f"Failed to load agent config {agent_type_str}: {e}")
                else:
                    self.logger.warning("Invalid agent configurations data format in config file")

            # Ensure we have default configurations
            self._ensure_default_configurations()

        except json.JSONDecodeError as e:
            self.logger.warning(f"Configuration file is corrupted (JSON decode error: {e}). Using default configurations.")
            self._load_default_configurations()
        except Exception as e:
            self.logger.error(f"Failed to load configurations: {e}")
            self._load_default_configurations()
    
    def _ensure_default_configurations(self):
        """Ensure default providers and agent configurations are available"""
        # Add missing default providers
        default_providers = get_default_providers()
        for name, provider in default_providers.items():
            if name not in self._providers:
                self._providers[name] = provider
        
        # Add missing default agent configurations
        default_agent_configs = get_default_agent_configurations()
        for agent_type, config in default_agent_configs.items():
            if agent_type not in self._agent_configs:
                self._agent_configs[agent_type] = config
    
    def _load_default_configurations(self):
        """Load default configurations as fallback"""
        self.logger.info("Loading default configurations")
        self._providers = get_default_providers()
        self._agent_configs = get_default_agent_configurations()
    
    def _save_configurations(self):
        """Save current configurations to file"""
        try:
            data = {
                "providers": {
                    name: provider.to_dict() 
                    for name, provider in self._providers.items()
                },
                "agent_configurations": {
                    agent_type.value: config.to_dict()
                    for agent_type, config in self._agent_configs.items()
                },
                "last_updated": datetime.now().isoformat()
            }
            
            with open(self._config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            self.logger.debug("Configurations saved successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to save configurations: {e}")
    
    # Provider Management Methods
    
    def register_provider(self, provider: AIProvider) -> Tuple[bool, List[str]]:
        """Register a new provider"""
        # Validate provider configuration
        errors = validate_provider_config(provider)
        if errors:
            return False, errors
        
        # Check if provider already exists
        if provider.name in self._providers:
            return False, [f"Provider '{provider.name}' already exists"]
        
        # Add provider
        self._providers[provider.name] = provider
        self._save_configurations()
        
        self.logger.info(f"Provider '{provider.name}' registered successfully")
        return True, []
    
    def update_provider(self, provider: AIProvider) -> Tuple[bool, List[str]]:
        """Update existing provider"""
        # Validate provider configuration
        errors = validate_provider_config(provider)
        if errors:
            return False, errors
        
        # Check if provider exists
        if provider.name not in self._providers:
            return False, [f"Provider '{provider.name}' does not exist"]
        
        # Update provider
        self._providers[provider.name] = provider
        self._save_configurations()
        
        self.logger.info(f"Provider '{provider.name}' updated successfully")
        return True, []
    
    def remove_provider(self, provider_name: str) -> bool:
        """Remove a provider"""
        if provider_name not in self._providers:
            return False
        
        # Check if provider is being used by any agent
        for agent_type, config in self._agent_configs.items():
            if config.provider_name == provider_name:
                self.logger.warning(f"Cannot remove provider '{provider_name}' - used by agent {agent_type.value}")
                return False
        
        del self._providers[provider_name]
        self._save_configurations()
        
        self.logger.info(f"Provider '{provider_name}' removed successfully")
        return True
    
    def get_provider(self, provider_name: str) -> Optional[AIProvider]:
        """Get provider by name"""
        return self._providers.get(provider_name)
    
    def get_all_providers(self) -> Dict[str, AIProvider]:
        """Get all registered providers"""
        return self._providers.copy()
    
    def get_active_providers(self) -> Dict[str, AIProvider]:
        """Get only active providers"""
        return {
            name: provider for name, provider in self._providers.items()
            if provider.is_active
        }
    
    def validate_provider(self, provider_name: str) -> Tuple[bool, List[str]]:
        """Validate provider configuration"""
        provider = self.get_provider(provider_name)
        if not provider:
            return False, [f"Provider '{provider_name}' not found"]
        
        return len(validate_provider_config(provider)) == 0, validate_provider_config(provider)
    
    def get_available_models(self, provider_name: str) -> List[str]:
        """Get available models for provider"""
        provider = self.get_provider(provider_name)
        return provider.supported_models if provider else []
    
    # Agent Configuration Methods
    
    def configure_agent(self, agent_type: AgentType, config: AgentConfiguration) -> Tuple[bool, List[str]]:
        """Configure agent with provider and model"""
        from ..config.agent_configs import validate_agent_configuration
        
        # Validate configuration
        errors = validate_agent_configuration(config)
        if errors:
            return False, errors
        
        # Check if provider exists and is active
        provider = self.get_provider(config.provider_name)
        if not provider:
            return False, [f"Provider '{config.provider_name}' not found"]
        
        if not provider.is_active:
            return False, [f"Provider '{config.provider_name}' is not active"]
        
        # Check if model is supported by provider
        if config.model_name not in provider.supported_models:
            return False, [f"Model '{config.model_name}' not supported by provider '{config.provider_name}'"]
        
        # Update configuration
        self._agent_configs[agent_type] = config
        self._save_configurations()
        
        self.logger.info(f"Agent '{agent_type.value}' configured with provider '{config.provider_name}' and model '{config.model_name}'")
        return True, []
    
    def get_agent_config(self, agent_type: AgentType) -> Optional[AgentConfiguration]:
        """Get configuration for specific agent"""
        return self._agent_configs.get(agent_type)
    
    def get_all_agent_configs(self) -> Dict[AgentType, AgentConfiguration]:
        """Get all agent configurations"""
        return self._agent_configs.copy()
    
    def reset_agent_config(self, agent_type: AgentType) -> bool:
        """Reset agent configuration to default"""
        from ..config.agent_configs import get_default_agent_configurations
        
        default_configs = get_default_agent_configurations()
        if agent_type in default_configs:
            self._agent_configs[agent_type] = default_configs[agent_type]
            self._save_configurations()
            self.logger.info(f"Agent '{agent_type.value}' configuration reset to default")
            return True
        return False
    
    # Utility Methods
    
    def get_provider_status_summary(self) -> Dict[str, Any]:
        """Get summary of all provider statuses"""
        summary = {
            "total_providers": len(self._providers),
            "active_providers": len(self.get_active_providers()),
            "providers": {}
        }
        
        for name, provider in self._providers.items():
            summary["providers"][name] = {
                "display_name": provider.display_name,
                "provider_type": provider.provider_type.value,
                "is_active": provider.is_active,
                "test_status": provider.test_status,
                "last_tested": provider.last_tested,
                "model_count": len(provider.supported_models)
            }
        
        return summary

# Global instance
_provider_manager = None

def get_provider_manager() -> ProviderManager:
    """Get global provider manager instance"""
    global _provider_manager
    if _provider_manager is None:
        _provider_manager = ProviderManager()
    return _provider_manager
